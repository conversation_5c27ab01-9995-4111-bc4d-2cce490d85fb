
/**
 * High-Performance Transparent Proxy Server
 * Features:
 * - No KV storage for maximum speed
 * - Domain whitelist control via global variable
 * - Environment variable based URL routing
 * - Pure transparent data forwarding
 */

// Global configuration - Domain whitelist (performance optimized)
const ALLOWED_DOMAINS_RAW = Deno.env.get("ALLOWED_DOMAINS")?.split(",") || ["*"];
const ALLOW_ALL_DOMAINS = ALLOWED_DOMAINS_RAW.includes("*");
const EXACT_DOMAINS = new Set<string>();
const WILDCARD_DOMAINS: string[] = [];

// Pre-process domains for O(1) exact lookups and optimized wildcard matching
ALLOWED_DOMAINS_RAW.forEach((domain: string) => {
  if (domain.startsWith("*.")) {
    WILDCARD_DOMAINS.push(domain.slice(2));
  } else if (domain !== "*") {
    EXACT_DOMAINS.add(domain);
  }
});

// Environment variable for URL prefix routing
const PROXY_PREFIX = `/${Deno.env.get("PROXY_PREFIX") || "proxy"}/`;
const PROXY_PREFIX_LENGTH = PROXY_PREFIX.length;

// Performance optimization: Pre-compile regex patterns
const PROXY_PREFIX_REGEX = new RegExp(`^${PROXY_PREFIX.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`);

/**
 * Check if domain is allowed based on whitelist (optimized for performance)
 * @param domain - Domain to check
 * @returns boolean - Whether domain is allowed
 */
function isDomainAllowed(domain: string): boolean {
  // Fast path: allow all domains
  if (ALLOW_ALL_DOMAINS) return true;

  // O(1) exact domain lookup
  if (EXACT_DOMAINS.has(domain)) return true;

  // Optimized wildcard matching (only if needed)
  for (const baseDomain of WILDCARD_DOMAINS) {
    if (domain === baseDomain || domain.endsWith(`.${baseDomain}`)) {
      return true;
    }
  }

  return false;
}

/**
 * Extract target URL from request path (performance optimized)
 * @param pathname - Request pathname
 * @returns string | null - Target URL or null if invalid
 */
function extractTargetUrl(pathname: string): string | null {
  // Fast path: check prefix length first (micro-optimization)
  if (pathname.length <= PROXY_PREFIX_LENGTH || !pathname.startsWith(PROXY_PREFIX)) {
    return null;
  }

  // Remove proxy prefix and get the target URL
  const targetUrl = pathname.slice(PROXY_PREFIX_LENGTH);

  // Validate URL format and domain in one step
  try {
    const url = new URL(targetUrl);

    // Fast domain check
    return isDomainAllowed(url.hostname) ? targetUrl : null;
  } catch {
    return null;
  }
}

// Pre-defined hop-by-hop headers for performance (avoid array creation on each request)
const HOP_BY_HOP_HEADERS = [
  'connection',
  'keep-alive',
  'proxy-authenticate',
  'proxy-authorization',
  'te',
  'trailers',
  'transfer-encoding',
  'upgrade'
] as const;

/**
 * Create optimized headers for proxy response (performance optimized)
 * @param originalHeaders - Original response headers
 * @returns Headers - Optimized headers
 */
function createProxyHeaders(originalHeaders: Headers): Headers {
  const headers = new Headers(originalHeaders);

  // Remove hop-by-hop headers for transparent proxying (optimized loop)
  for (const header of HOP_BY_HOP_HEADERS) {
    headers.delete(header);
  }

  return headers;
}

// CORS headers for cross-origin support (pre-defined for performance)
const CORS_HEADERS = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH",
  "Access-Control-Allow-Headers": "*",
  "Access-Control-Max-Age": "86400"
} as const;

// Main server handler (performance optimized)
Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests immediately (fast path)
  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 204,
      headers: CORS_HEADERS
    });
  }

  const url = new URL(req.url);

  // Extract target URL from path
  const targetUrl = extractTargetUrl(url.pathname);

  if (!targetUrl) {
    return new Response("404 Not Found", {
      status: 404,
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        ...CORS_HEADERS
      }
    });
  }

  // Construct final URL with query parameters (optimized string concatenation)
  const finalUrl = url.search ? targetUrl + url.search : targetUrl;

  try {
    // Create transparent proxy request (optimized for performance)
    const proxyRequest = new Request(finalUrl, {
      method: req.method,
      headers: req.headers,
      body: req.body,
      keepalive: true, // Connection reuse for better performance
    });

    // Forward request to target
    const targetResponse = await fetch(proxyRequest);

    // Create optimized response headers with CORS support
    const responseHeaders = createProxyHeaders(targetResponse.headers);

    // Add CORS headers to response
    Object.entries(CORS_HEADERS).forEach(([key, value]) => {
      responseHeaders.set(key, value);
    });

    // Return transparent response with streaming body
    return new Response(targetResponse.body, {
      status: targetResponse.status,
      statusText: targetResponse.statusText,
      headers: responseHeaders,
    });

  } catch (error) {
    // Return error response with CORS headers
    return new Response(
      `Proxy Error: ${error instanceof Error ? error.message : String(error)}`,
      {
        status: 502,
        headers: {
          "Content-Type": "text/plain; charset=utf-8",
          ...CORS_HEADERS
        }
      }
    );
  }
});